import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
import axios from "axios";
// 分页
export const vectorPage = async (data: any) => {
  return await axios.get("/api/business/vector/tile/page", { params: data });
};
// 新增
export const addVector = async (data: any) => {
  return await axios.post("/api/business/vector/tile", data);
};
// 修改
export const editVector = async (data: any) => {
  return await axios.put("/api/business/vector/tile", data);
};
// 详情
export const detailVector = async (id: string) => {
  return await axios.get(`/api/business/vector/tile/${id}`);
};
// 删除
export const delVector = async (id: string) => {
  return await axios.delete(`/api/business/vector/tile/${id}`);
};
