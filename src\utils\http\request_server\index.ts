import axios from "axios";
// import { ElMessage } from "element-plus";
// axios中定义好的接口
import type { AxiosInstance } from "axios";
import type { HYRequestInterceptors, HYRequestConfig } from "./type";
import router from "@/router";
import localCache from "@/utils/auth";
import { ElMessage } from "element-plus";
// import { ElLoading } from "element-plus";

// const DEAFULT_LOADING = true;
class Http {
  instance: AxiosInstance;
  interceptors?: HYRequestInterceptors;
  // showLoading: boolean;
  // loading: any;

  constructor(config: HYRequestConfig) {
    this.instance = axios.create(config);

    // 保存基本信息
    // this.showLoading = config.showLoading ?? DEAFULT_LOADING;
    this.interceptors = config.interceptors;

    // 使用拦截器
    // 1.从config中取出的拦截器是对应的实例的拦截器
    this.instance.interceptors.request.use(
      this.interceptors?.requestInterceptor,
      this.interceptors?.requestInterceptorCatch
    );
    this.instance.interceptors.response.use(
      this.interceptors?.responseInterceptor,
      this.interceptors?.responseInterceptorCatch
    );

    // 2.添加所有的实例都有的拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // if (this.showLoading) {
        //   this.loading = ElLoading.service({
        //     lock: true,
        //     text: "正在请求数据....",
        //     background: "rgba(0, 0, 0, 0.5)",
        //   });
        // }
        return config;
      },
      (err) => {
        return err;
      }
    );

    this.instance.interceptors.response.use(
      (res: any) => {
        const data = res.data;
        const response = res.response;
        // 处理登录问题
        if (res.config.url === "../auth/login") {
          if (response?.status === 403) {
            localCache.clearCache();
            ElMessage({
              message: "用户名或密码错误",
              type: "error"
            });
            return false;
          } else {
            if (res.data === undefined) {
              localCache.setCache("X-TOKEN", "");
            } else {
              localCache.setCache("X-TOKEN", res.data);
            }
            return true;
          }
        }
        if (response?.status === 403) {
          return router.replace("/login");
        }
        // 授权验证失败
        if (response?.status === 401) {
          ElMessage({
            message: response?.data,
            type: "error"
          });
          void router.replace("/login");
          return response;
        }
        if (res.request?.responseType !== "blob" && res.status !== 200 && res.status !== 201) {
          ElMessage({
            message: response?.data,
            type: "error"
          });
          throw response?.data;
        }
        return data;
      },
      async (err) => {
        // ElMessage({
        //   message: err.msg,
        //   type: "error"
        // });
        return await Promise.reject(err);
      }
    );
  }

  async request<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await new Promise((resolve, reject) => {
      // 1.单个请求对请求config的处理
      if (config.interceptors?.requestInterceptor != null) {
        config = config.interceptors.requestInterceptor(config);
      }

      // 2.判断是否需要显示loading
      // if (config.showLoading === false) {
      //   this.showLoading = config.showLoading;
      // }

      this.instance
        .request<any, T>(config)
        .then((res) => {
          // 1.单个请求对数据的处理
          if (config.interceptors?.responseInterceptor != null) {
            res = config.interceptors.responseInterceptor(res);
          }
          // 2.将showLoading设置true, 这样不会影响下一个请求
          // this.showLoading = DEAFULT_LOADING;

          // 3.将结果resolve返回出去
          resolve(res);
        })
        .catch((err) => {
          // 将showLoading设置true, 这样不会影响下一个请求
          // this.showLoading = DEAFULT_LOADING;
          reject(err);
          return err;
        });
    });
  }

  async get<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: "GET" });
  }

  async post<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: "POST" });
  }

  async put<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: "PUT" });
  }

  async delete<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: "DELETE" });
  }

  async patch<T = any>(config: HYRequestConfig<T>): Promise<T> {
    return await this.request<T>({ ...config, method: "PATCH" });
  }
}

export default Http;
