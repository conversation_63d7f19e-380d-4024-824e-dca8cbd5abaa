<!--
 * @Description: 
 * @Date: 2023-02-03 14:28:09
 * @Author: GISerZ
 * @LastEditors: Tanqy
 * @LastEditTime: 2024-04-09 09:53:56
-->
<template>
  <div class="login-container">
    <div class="login-bg-top"></div>
    <div class="login-bg-bottom"></div>
    <CopyRight />
    <el-card class="box-card" shadow="never">
      <div class="card-header">您好！欢迎登录</div>
      <div class="divider-box">
        <span>{{ title }}</span>
      </div>
      <el-form ref="ruleFormRef" class="login-form" :model="ruleForm" :rules="rules">
        <el-form-item prop="userName">
          <el-input v-model="ruleForm.userName" placeholder="请输入登录账号">
            <template #prefix>
              <el-icon class="el-input__icon">
                <UserIcon />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" placeholder="请输入登录密码" v-model="ruleForm.password">
            <template #prefix>
              <el-icon class="el-input__icon">
                <LockIcon />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item> </el-form-item>
        <el-row class="login-btn-row" justify="center">
          <el-button type="primary" class="login-btn" @click="submitForm()">登录</el-button>
        </el-row>
      </el-form>
    </el-card>
    <el-dialog width="20%" v-model="dlgVisible" title="授权码输入">
      <el-input v-model="licenseVal" placeholder="请输入许可授权码"></el-input>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            :disabled="!(licenseVal.length > 0)"
            type="primary"
            :loading="lcsBtnLoading"
            @click="subLicense"
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
// import { login } from "@/api/login";
import { ElForm } from "element-plus";
import router from "@/router";
import store from "@/store";
import localCatch from "@/utils/auth";
import moment from "moment";
import { getRoleUser } from "@/api/security/role";
import UserIcon from "./components/UserIcon";
import LockIcon from "./components/LockIcon";
import CopyRight from "./components/CopyRight.vue";
import { verify, verifySave } from "@/api/auth";
const title = ref(process.env.VUE_APP_NAME);
// 表单输入规则
const rules = {
  userName: [
    {
      required: true, // 是否必须字段
      message: "请输入用户名", // 触发的提示信息
      trigger: "blur" // 触发时机: 当失去焦点时（光标不显示的时候），触发此提示
    },
    {
      min: 0, // 最小字符书
      max: 20, // 最大字符数
      message: "用户名长度需要在3-10个字符之间", // 触发的提示信息
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true, // 是否必须字段
      message: "请输入密码", // 触发的提示信息
      trigger: "blur" // 触发时机: 当失去焦点时（光标不显示的时候），触发此提示
    }
    /*     {
      min: 3, // 最小字符书
      max: 15, // 最大字符数
      message: "密码长度需要在3-15个字符之间", // 触发的提示信息
      trigger: "blur"
    } */
  ]
};
const dlgVisible = ref(false);
const licenseVal = ref("");
const lcsBtnLoading = ref(false);
const subLicense = async () => {
  lcsBtnLoading.value = true;
  const result: any = await verifySave(licenseVal.value);
  if (result?.status === 401) {
    dlgVisible.value = true;
  } else {
    dlgVisible.value = false;
  }
  lcsBtnLoading.value = false;
};
// 登录
const ruleFormRef = ref<InstanceType<typeof ElForm>>();
const ruleForm = reactive({
  userName: "",
  password: ""
});
const setUserRole = async (username: string) => {
  const temPromise: any = await getRoleUser(username);
  const roles: string[] = temPromise.roles;
  localCatch.setCache("userRoles", roles);
};
const submitForm = () => {
  console.log("login");
  // 对表单内容进行验证
  ruleFormRef.value?.validate(async (valid) => {
    if (valid) {
      await store.dispatch("login/login", ruleForm);
      const token = localCatch.getCache("X-TOKEN");
      if (token && (store.state as any).login.loginStatus) {
        const temData = moment().format("YYYY/MM/DD HH:mm:ss");
        localCatch.setCache("loginTime", temData);
        localCatch.setCache("realName", ruleForm.userName);
        // 记录用户角色
        setUserRole(ruleForm.userName);
        router.push("/home");
      }
    } else {
      console.log("error submit!");
      return false;
    }
  });
};
const verifyshow = async () => {
  const result: any = await verify();
  if (result?.status === 401) {
    dlgVisible.value = true;
  }
};
onMounted(() => {
  const historyLogin = localCatch.getCache("loginTime");
  console.log(historyLogin);
  localCatch.setCache("historyLogin", historyLogin);
  verifyshow();
});
</script>
<style scoped lang="scss">
.login-container {
  width: 100%;
  height: 100vh;
  position: relative;

  .login-bg-top {
    width: inherit;
    // height: 638px;
    height: calc(638px / 1080px * 100%);
    background: url("@/assets/img/login/login.jpg") no-repeat;
    background-size: 100% 100%;
  }

  .login-bg-bottom {
    width: inherit;
    // height: calc(100vh - 638px);
    height: calc(100vh - 638px / 1080px * 100%);
    background-color: #f4f7fa;
  }

  .box-card {
    width: 560px;
    height: 640px;
    position: absolute;
    right: 238px;
    bottom: 140px;
    border: unset;

    .card-header {
      font-size: 40px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      color: #232a3a;
      text-align: center;
      margin-top: 24px;
    }
  }

  .divider-box {
    text-align: center;
    margin: 16px 0;

    span {
      position: relative;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: #4b5970;

      &:before,
      &:after {
        content: "";
        position: absolute;
        top: 50%;
        width: 16px;
        height: 1px;
        background-color: #bac6d5;
      }

      &:before {
        left: -40px;
      }

      &:after {
        right: -40px;
      }
    }
  }

  .login-form {
    margin: 40px;
    margin-top: 48px;

    :deep(.el-input__inner) {
      height: 56px;
      line-height: 56px;
    }

    .el-form-item {
      margin-bottom: 24px;
    }

    .login-btn-row {
      margin-top: 40px;
    }

    .login-btn {
      width: 100%;
      height: 56px;
      border-radius: 28px;
      font-size: 20px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: #ffffff;

      --el-button-bg-color: #4771fe;
      --el-button-border-color: #4771fe;
      --el-button-hover-bg-color: #698bfd;
      --el-button-hover-border-color: #698bfd;

      &:active {
        --el-button-active-bg-color: #3d6bff;
        --el-button-active-border-color: #3d6bff;
      }

      &:focus-visible {
        --el-button-outline-color: #5c82ff;
      }
    }
  }
}
</style>
