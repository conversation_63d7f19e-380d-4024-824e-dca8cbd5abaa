<!--
 * @Description: 服务卡片
 * @Autor: silei
 * @Date: 2023-02-01 09:29:36
 * @LastEditors: silei
 * @LastEditTime: 2024-05-29 16:51:56
-->
<template>
  <div class="service-card" @click="serviceClick">
    <el-row v-if="showThumbnail">
      <el-image
        v-if="modelValue.serviceTypeString === '地图服务'"
        class="service-thumbnail"
        :src="modelValue.thumbnail + '?' + token"
      >
        <template #error>
          <div class="image-slot">
            <img class="service-thumbnail" src="../../assets/img/server-2d.png" alt="" />
          </div>
        </template>
      </el-image>
      <img
        v-else-if="
          modelValue.serviceTypeString === '三维服务' || modelValue.serviceTypeString === '地形服务'
        "
        class="service-thumbnail"
        src="../../assets/img/server-3d.png"
        alt=""
      />
      <img v-else class="service-thumbnail" src="../../assets/img/server-2d.png" alt="" />
    </el-row>
    <el-row>
      <span class="title" type="primary">{{ modelValue.title ?? modelValue.name }}</span>
      <span v-if="modelValue.errorMsg" class="status-error" type="primary">异常</span>
      <span v-else class="status-normal" type="primary">正常</span>
    </el-row>
    <el-row>
      <span class="title-assistant" type="primary"
        >浏览量 {{ modelValue.visits }} · {{ date }}</span
      >
    </el-row>
    <div class="card-footer">
      <div class="item-start" v-if="!modelValue.enabled" @click.stop="setServiceEnabled(true)">
        <img src="../../assets/img/card-start.png" alt="" />
        <span>启动</span>
      </div>
      <div class="item-stop" v-else @click.stop="setServiceEnabled(false)">
        <img src="../../assets/img/card-stop.png" alt="" />
        <span>停用</span>
      </div>
      <div class="item-del" @click.stop="deleteService">
        <img src="../../assets/img/card-del.png" alt="" />
        <span>删除</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setScoped } from "@/utils/basic";
import { PropType, onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { BaseServiceInfo } from "geoserver-manager";
import localCache from "@/utils/auth";
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
const router = useRouter();
// 定义属性
const props = defineProps({
  modelValue: {
    type: Object as PropType<BaseServiceInfo>,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      name: "",
      workspace: "",
      thumbnail: "",
      types: [],
      visits: 0,
      enabled: true,
      dateModified: 0
    }
  },
  showThumbnail: {
    type: Boolean,
    default: true
  }
});
const date = computed(() => {
  return moment(props.modelValue.dateModified).format("yyyy-MM-DD");
});
// 服务被点击时触发
const serviceClick = () => {
  router.push(`./${props.modelValue.type}/${props.modelValue.name}`);
};
const setServiceEnabled = async (enabled: boolean) => {
  if (enabled) {
    setScoped(`服务启动-${props.modelValue.title ?? props.modelValue.name}`);
    ElMessage({
      message: "启动成功",
      type: "success"
    });
  } else {
    setScoped(`服务停用-${props.modelValue.title ?? props.modelValue.name}`);
    ElMessage({
      message: "停用成功",
      type: "success"
    });
  }
  props.modelValue.setEnabled(enabled);
};
const emits = defineEmits(["deleted"]);
const deleteService = async () => {
  ElMessageBox.confirm("确定要删除当前服务吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox"
  }).then(async () => {
    await props.modelValue.delete();
    emits("deleted");
    ElMessage({
      message: "删除成功",
      type: "success"
    });
  });
};
const token = ref("");
onMounted(() => {
  const tokenCache: string = localCache.getCache("X-TOKEN");
  tokenCache.replace("Bearer ", "");
  token.value = `Bearer=${tokenCache}`;
});
</script>
<style scoped lang="scss">
.service-card {
  cursor: pointer;
  background: #ffffff;
  width: calc((100% - 78px) / 5);
  height: 266px;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin: 10px 17px 10px 0;
  .service-thumbnail {
    width: 268px;
    height: 150px;
    margin: 10px auto;
  }
  .status-normal,
  .status-error {
    margin-left: 16px;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: Source Han Sans CN, Source Han Sans CN-Bold;
    font-weight: 500;
  }
  .status-normal {
    border: 1px solid #07da65;
    color: #07da65;
  }
  .status-error {
    border: 1px solid #ff5353;
    color: #ff5353;
  }
}
.service-card:nth-child(5) {
  margin: 10px 0px 10px 0;
}
.service-card:nth-child(10) {
  margin: 10px 0px 10px 0;
}
.title {
  margin-left: 10px;
  font-size: 18px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  color: #232a3a;
}
.title-assistant {
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  color: #7d8da1;
  margin: 6px 0 6px 10px;
}
.card-footer {
  width: 100%;
  height: 40px;
  border-top: 1px solid #e1e7eb;
  display: flex;
  align-items: center;
  .item-start {
    width: 50%;
    height: 100%;
    border-right: 1px solid #e1e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
      margin: 3px 6px 0 6px;
    }
    span {
      font-size: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC-Regular;
      font-weight: 400;
      color: #4076f3;
    }
  }
  .item-del {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
      margin: 3px 6px 0 6px;
    }
    span {
      font-size: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC-Regular;
      font-weight: 400;
      color: #334d6e;
    }
  }
  .item-stop {
    width: 50%;
    height: 100%;
    border-right: 1px solid #e1e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
      margin: 3px 6px 0 6px;
    }
    span {
      font-size: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC-Regular;
      font-weight: 400;
      color: #ff5353;
    }
  }
}
</style>
