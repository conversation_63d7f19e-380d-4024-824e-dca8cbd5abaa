/*
 * @Author: xiao
 * @Date: 2022-05-10 09:56:04
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 10:20:27
 * @Description:
 */
// 出口文件
import Http from "./index";
// import axios from 'axios'
// import { getToken } from "@/utils/auth";
import localCatch from "@/utils/auth";
import { logAdd } from "@/api/log";
import { reactive } from "vue";
import moment from "moment";
// import NProgress from "@/utils/progress";
// import { ElMessage } from "element-plus";
const form: any = reactive({
  title: "",
  businessType: "",
  requestMethod: "",
  operUrl: "",
  operIp: "",
  operParam: "",
  jsonResult: "",
  status: "",
  errorMsg: "",
  operTime: "",
  costTime: ""
});

const hRequest = new Http({
  baseURL: `/${process.env.VUE_APP_SERVER_BASEURL!}/api`,
  timeout: 40000,
  interceptors: {
    requestInterceptor: (config) => {
      // NProgress.start();
      if (Boolean(config.params) && Boolean(config.params.from) && config.params.from.length > 0) {
        config.headers!.Accept = "application/csv";
      }
      const token = localCatch.getCache("Latias");
      // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
      if (token) {
        // config.headers!.Authorization = token;
        config.headers!.Latias = token;
      }
      return config;
    },
    requestInterceptorCatch: (err) => {
      return err;
    },
    responseInterceptor: (res) => {
      if (res.config.method !== "get") {
        form.title = localCatch.getCache("route_title");
        form.businessType =
          res.config.method === "post" ? "1" : res.config.method === "put" ? "2" : "3";
        form.requestMethod = res.config.method;
        form.operName = localCatch.getCache("realName");
        form.operUrl = res.request.responseURL;
        form.operIp = "";
        form.operParam = "";
        form.jsonResult = res.data;
        form.status = res.status === 200 ? 1 : 0;
        form.errorMsg = res.statusText;
        form.operTime = moment(res.headers.date).format("YYYY-MM-DD HH:mm:ss");
        form.costTime = res.config.timeout;
        const result = logAdd(form);
        console.log(result);
      }
      // console.log(res);
      // NProgress.done();
      return res;
    },
    responseInterceptorCatch: (err) => {
      // err.toString().includes("timeout") !== null
      //   ? ElMessage({ type: "error", message: "超时，请重试" })
      //   : "";
      // NProgress.done();
      return err;
    }
  }
});

export default hRequest;
