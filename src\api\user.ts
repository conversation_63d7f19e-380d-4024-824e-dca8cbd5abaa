import axios from "axios";
import hRequest from "@/utils/http/request_server/hRequest";
import type { SysUserDto, UserListResponse } from "@/interface/security/user";
/**
 * @description 获取用户列表
 * @returns {Promise<UserListResponse>} 用户列表响应
 */
export const getUserList = async (): Promise<UserListResponse> => {
  const result = await axios.get<UserListResponse>("/api/sys/user/list");
  return result.data;
};

export const getCurrentUser = async (token: string): Promise<SysUserDto> => {
  const result = await axios.get<any>("/api/sys/user/current", { params: { token } });
  return result.data?.data;
};
