<!--
 * @Description: 服务卡片
 * @Autor: silei
 * @Date: 2023-02-01 09:29:36
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-01-02 10:24:33
-->
<template>
  <div class="service-card" @click="serviceClick">
    <el-row>
      <el-image
        class="service-thumbnail"
        :class="[modelValue.serverType == 'spatial' ? 'spatial-service' : '']"
        :src="modelValue.thumbnail + '?' + token"
      >
        <template #error>
          <div class="image-slot">
            <img
              class="service-thumbnail"
              :class="[modelValue.serverType == 'spatial' ? 'spatial-service' : '']"
              src="@/assets/img/server-2d.png"
              alt=""
            />
          </div>
        </template>
      </el-image>
    </el-row>
    <el-row>
      <span class="title" type="primary">{{ modelValue.title ?? modelValue.name }}</span>
    </el-row>
    <el-row>
      <span class="title-assistant" type="primary"
        >浏览量 {{ modelValue.visits }} · {{ date }}</span
      >
    </el-row>
    <div class="card-footer" v-if="modelValue.serverType === 'network'">
      <div class="item-start" v-if="!modelValue.enabled" @click.stop="setServiceEnabled()">
        <img src="@/assets/img/card-start.png" alt="" />
        <span>启动</span>
      </div>
      <div class="item-stop" v-else @click.stop="setServiceEnabled()">
        <img src="@/assets/img/card-stop.png" alt="" />
        <span>停用</span>
      </div>
      <div class="item-del" @click.stop="deleteService()">
        <img src="@/assets/img/card-del.png" alt="" />
        <span>删除</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
import localCache from "@/utils/auth";
import { ElMessageBox } from "element-plus";
import moment from "moment";
const router = useRouter();
// 定义属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  },
  showThumbnail: {
    type: Boolean,
    default: true
  }
});
const date = computed(() => {
  return moment(props.modelValue.dateModified).format("yyyy-MM-DD");
});
// 服务被点击时触发
const serviceClick = () => {
  let type = "map";
  if ((props.modelValue as any).serverType === "scene3d") {
    type = "scene";
  }
  router.push({
    path: `../${type}/${props.modelValue.name}`,
    query: {
      type: (props.modelValue as any).serverType
    }
  });
};
const setServiceEnabled = async () => {
  emits("setEnabled");
};
const emits = defineEmits(["deleted", "setEnabled"]);
const deleteService = async () => {
  let tip = "";
  if (props.modelValue.serverType === "network") {
    tip = "确定要删除当前网络分析吗？";
  }
  ElMessageBox.confirm(tip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox"
  }).then(async () => {
    emits("deleted");
  });
};
const token = ref("");
onMounted(() => {
  const tokenCache: string = localCache.getCache("X-TOKEN");
  tokenCache.replace("Bearer ", "");
  token.value = `Bearer=${tokenCache}`;
});
</script>
<style scoped lang="scss">
.service-card {
  cursor: pointer;
  background: #ffffff;
  width: calc((100% - 78px) / 5);
  height: 266px;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin: 10px 17px 10px 0;
  .service-thumbnail {
    width: 268px;
    height: 150px;
    margin: 10px auto;
  }
  .spatial-service {
    width: 268px;
    height: 185px;
    margin: 10px auto;
  }
}

.service-card:nth-child(5) {
  margin: 10px 0px 10px 0;
}
.service-card:nth-child(10) {
  margin: 10px 0px 10px 0;
}
.title {
  margin-left: 10px;
  font-size: 18px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  color: #232a3a;
}
.title-assistant {
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  color: #7d8da1;
  margin: 6px 0 6px 10px;
}
.card-footer {
  width: 100%;
  height: 40px;
  border-top: 1px solid #e1e7eb;
  display: flex;
  align-items: center;
  .item-start {
    width: 50%;
    height: 100%;
    border-right: 1px solid #e1e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
      margin: 3px 6px 0 6px;
    }
    span {
      font-size: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC-Regular;
      font-weight: 400;
      color: #4076f3;
    }
  }
  .item-del {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
      margin: 3px 6px 0 6px;
    }
    span {
      font-size: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC-Regular;
      font-weight: 400;
      color: #334d6e;
    }
  }
  .item-stop {
    width: 50%;
    height: 100%;
    border-right: 1px solid #e1e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
      margin: 3px 6px 0 6px;
    }
    span {
      font-size: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC-Regular;
      font-weight: 400;
      color: #ff5353;
    }
  }
}
</style>
