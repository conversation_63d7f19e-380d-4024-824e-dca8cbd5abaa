<template>
  <div class="common-layout">
    <router-view></router-view>
  </div>
</template>
<script setup lang="ts">
import { useRoute } from "vue-router";
import localCatch from "@/utils/auth";
import { getCurrentUser } from "@/api/user";
import moment from "moment";
import { getRoleUser } from "@/api/security/role";
import { useStore } from "vuex";
const route = useRoute(); // 活跃状态的路由
const store = useStore();
const initUserInfo = async () => {
  const token = route.query.token || localCatch.getCache("X-TOKEN");
  if (token) {
    localCatch.setCache("X-TOKEN", token);
    // 获取当前用户信息
    const data = await getCurrentUser(token as string);
    console.log(data);
    if (data) {
      await store.commit("login/changeUserName", data.nickname);
      const temData = moment().format("YYYY/MM/DD HH:mm:ss");
      localCatch.setCache("loginTime", temData);
      localCatch.setCache("realName", "admin");
      // 记录用户角色
      setUserRole("admin");
    }
  }
};
const setUserRole = async (username: string) => {
  const temPromise: any = await getRoleUser(username);
  const roles: string[] = temPromise.roles;
  localCatch.setCache("userRoles", roles);
};
initUserInfo();
</script>
