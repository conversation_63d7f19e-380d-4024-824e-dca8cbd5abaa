<!--
 * @Description: 服务信息
 * @Autor: silei
 * @Date: 2023-02-01 11:34:24
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-22 15:22:13
-->
<template>
  <div class="custom-content">
    <div class="info-bg">
      <el-row class="card-header" :gutter="32">
        <el-col class="title-left" :span="20"
          ><img @click="cancel" src="../../assets/img/server-back.png" alt="" /><span
            @click="cancel"
            >返回 <b>|</b></span
          >
          <div class="text">服务详情</div>
        </el-col>
        <el-col class="server-title-right" :span="4">
          <div @click="saveService" class="save">保存</div>
        </el-col>
      </el-row>
      <el-row class="serve-detial" :gutter="32">
        <el-col class="serve-detial-left" :span="6">
          <el-image class="defaultImg" :src="service.thumbnail + '?' + token">
            <template #error>
              <div class="image-slot">
                <img class="defaultImg" src="../../assets/img/server-2d.png" alt="" />
              </div>
            </template>
          </el-image>
        </el-col>
        <el-col class="serve-detial-right" :span="18">
          <div class="right-title">{{ service?.name }}</div>
          <div>
            <table border="1">
              <tr>
                <td class="title">服务标题</td>
                <td class="content">
                  <div class="server-edit">
                    <span>{{ service.title }}</span>
                  </div>
                </td>
                <td class="title">服务类型</td>
                <td class="content">
                  <span>{{ service?.serviceTypeString }}</span>
                </td>
              </tr>
              <tr>
                <td class="title">服务描述</td>
                <td class="content">
                  <div class="server-edit">
                    <span>{{ service.description }}</span>
                  </div>
                </td>
                <td class="title">服务预览</td>
                <td class="content">
                  <span
                    ><el-space wrap spacer="|">
                      <el-link
                        v-for="preview in previewUrl"
                        :key="preview.type"
                        @click="handlePreview(preview)"
                        >{{ preview.name }}</el-link
                      >
                    </el-space>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="title">服务地址</td>
                <td class="content" colspan="3">
                  <div class="server-url">
                    <el-row
                      class="server-url-item"
                      v-for="(serviceInfo, index) in service?.urls"
                      :key="index"
                    >
                      <el-link @click="handleOverview(serviceInfo.overviewUrl)">{{
                        serviceInfo.url
                      }}</el-link>
                    </el-row>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </el-col>
      </el-row>
      <div v-if="analysisType === 'network'">
        <el-row>
          <span class="divider-header">网络属性</span>
        </el-row>
        <div class="attribute-box">
          <NetWorkAnalysisFrom
            ref="networkFromRef"
            :labelPosition="'left'"
            :info="networkInfo"
          ></NetWorkAnalysisFrom>
        </div>
        <el-row>
          <span class="divider-header">网络分析</span>
        </el-row>
        <el-row class="network-analysis">
          <table border="1">
            <tr v-for="method in networkMethod" :key="method.name">
              <td class="title">{{ method.label }}</td>
              <td class="content">
                <div class="server-edit" @click="changeAnalysis(method.path)">
                  <span>{{ getNetworkPath(method.name) }}</span>
                </div>
              </td>
            </tr>
          </table>
        </el-row>
      </div>
      <div v-if="analysisType === 'spatial'">
        <el-row>
          <span class="divider-header">空间分析</span>
        </el-row>
        <el-row class="network-analysis">
          <table border="1">
            <tr v-for="method in spatialMethod" :key="method.name">
              <td class="title">{{ method.label }}</td>
              <td class="content">
                <div class="server-edit" @click="changeAnalysis(method.path)">
                  <span>{{ spatialUrl + method.name }}</span>
                </div>
              </td>
            </tr>
          </table>
        </el-row>
      </div>
      <!-- <div v-if="analysisType === 'scene3d'">
        <el-row>
          <span class="divider-header">三维分析</span>
        </el-row>
        <el-row class="network-analysis">
          <table border="1">
            <tr>
              <td class="title"></td>
              <td class="content">
                <div class="server-edit" @click="changeAnalysis('crossssection')">
                  <span>{{ crosssectionUrl }}</span>
                </div>
              </td>
            </tr>
          </table>
        </el-row>
      </div> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import NetWorkAnalysisFrom from "@/views/analysisServices/components/NetWorkAnalysisFrom.vue";
import { BaseSetting, PreviewAddress, ServiceType, ServiceUtil } from "geoserver-manager";
import { onMounted, ref, Ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import localCache from "@/utils/auth";
import { createTemToken } from "@/api/security/token";
import { setScoped } from "@/utils/basic";
import { detialNetwork, modifyNetwork } from "@/api/analysis/network";
const RoleAuthorizationShow = ref(false);
const networkFromRef = ref();
const route = useRoute();
const router = useRouter();
const service: any = ref({} as any);
const serviceList: any = ref([]);
const serviceType = route.params.serviceType as ServiceType;
const serviceName = route.params.serviceName as string;
const layerList: Ref<{ name: string; style: string }[]> = ref([]);
const previewUrl: Ref<PreviewAddress[]> = ref([]);
const cacheLayer: Ref<any> = ref({});
const analysisType = ref(route.query.type);
const loadServiceInfo = async () => {
  service.value = await ServiceUtil.getServiceInfo(serviceName, serviceType);
  serviceList.value = service.value.enabledServices.map((service: any) => service);
  cacheLayer.value = service.value.cacheLayer ?? {};
  cacheEnabled.value = service.value.cacheLayer?.enabled;
  selectFormats.value = service.value.cacheLayer?.formats;
  previewUrl.value = await service.value.getPreviewUrl();
  (service.value.type === ServiceType.MAP || service.value.type === ServiceType.POLYMERIZE) &&
    (layerList.value = service.value.layers);
};
const crosssectionUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/network/crossSectional`;
const shortestUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/network/shortest`;
const bufferUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/spatial/buffer`;
const traceUpUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/spatial/traceup`;
const traceDownUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/spatial/tracedown`;
const relationUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/spatial/georelation`;
const spatialUrl = BaseSetting.getBaseUrl() + `/${serviceName}/rest/spatial/`;
const spatialMethod = [
  {
    name: "buffer",
    label: "缓冲区分析",
    path: "Buffer"
  },
  {
    name: "georelation",
    label: "空间关系分析",
    path: "GeoRelation"
  },
  {
    name: "overlay",
    label: "叠加分析",
    path: "Overlay"
  },
  {
    name: "contour",
    label: "等值线分析",
    path: "Contour"
  }
];
const networkMethod = [
  {
    name: "crossSectional",
    label: "横断面分析",
    path: "CrosssectionAnalysis"
  },
  {
    name: "shortest",
    label: "最短路径分析",
    path: "ShortAnalysis"
  },
  {
    name: "traceUp",
    label: "上游分析",
    path: "TraceUp"
  },
  {
    name: "traceDown",
    label: "下游分析",
    path: "TraceDown"
  },
  {
    name: "horizontal",
    label: "水平净距分析",
    path: "Horizontal"
  },
  {
    name: "vertical",
    label: "垂直净距分析",
    path: "Vertical"
  },
  {
    name: "depth",
    label: "埋深分析",
    path: "Depth"
  },
  {
    name: "explosion",
    label: "爆管分析",
    path: "Explosion"
  },
  {
    name: "connectivity",
    label: "连通性分析",
    path: "Connectivity"
  },
  {
    name: "crossSectionalV",
    label: "纵断面分析",
    path: "CrossSectionalV"
  }
];
const getNetworkPath = (name: string) => {
  return BaseSetting.getBaseUrl() + `/${serviceName}/rest/network/${name}`;
};
const cacheEnabled: Ref<boolean> = ref(false);
const selectFormats: Ref<string[]> = ref([]);
const handleOverview = async (previewUrl: string) => {
  const userName = localCache.getCache("realName");
  const token = await createTemToken(userName, "");
  if (previewUrl.includes("?")) {
    window.open(previewUrl + `&token=${token}`, "_blank");
  } else {
    window.open(previewUrl + `?token=${token}`, "_blank");
  }
};
const handlePreview = (preview: PreviewAddress) => {
  const routeUrl = router.resolve({
    name: preview.url,
    query: preview.params
  });
  window.open(routeUrl.href, "_blank");
};
const cancel = () => {
  if (analysisType.value === "network") {
    router.push({
      name: "NetworkAnalysis"
    });
  } else {
    router.push({
      name: "SpatialAnalysis"
    });
  }
};
const token = ref("");
const changeAnalysis = (type: string) => {
  router.push({
    name: type,
    query: {
      name: serviceName
    }
  });
};
const saveService = async () => {
  const info = await networkFromRef.value.submitForm();
  console.log(info);
  if (!info) {
    return;
  }
  modifyNetwork({ pipeInfo: info }).then(() => {
    ElMessage({
      message: `修改成功`,
      type: "success"
    });
    setScoped(`网络分析修改-${info.title ?? info.name}`);
  });
  /*   router.push({
    name: "NetworkAnalysis"
  }); */
};
const networkInfo: any = ref({});
const getdetialNetwork = () => {
  detialNetwork(serviceName).then((res) => {
    networkInfo.value = res.pipeInfo;
  });
};
const initAnalysis = () => {
  if (analysisType.value === "network") {
    getdetialNetwork();
  }
};
onMounted(async () => {
  // 获取图层组
  await loadServiceInfo();
  await initAnalysis();
  RoleAuthorizationShow.value = true;
  const tokenCache: string = localCache.getCache("X-TOKEN");
  tokenCache.replace("Bearer ", "");
  token.value = `Bearer=${tokenCache}`;
});
</script>
<style lang="scss" scoped>
.service-info {
  width: 70%;
  margin: 25px auto;
}
span {
  font-size: 18px;
}
.info-bg {
  width: 100%;
  padding-bottom: 100px;
  background: #ffffff;
  min-height: 82vh;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
}
.card-header {
  height: 65px;
  line-height: 65px;
  border-bottom: 1px solid #ebebeb;
  margin-bottom: 18px;
  .title-left {
    display: flex;
    img {
      cursor: pointer;
      width: 16px;
      height: 16px;
      margin: 22px 10px 0 20px;
    }
    span {
      cursor: pointer;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #7d8da1;
      b {
        margin-left: 5px;
        font-weight: 100;
        color: rgba($color: #7d8da1, $alpha: 0.4);
      }
    }
    .text {
      margin: 0 10px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #232a3a;
    }
  }
  .server-title-right {
    display: flex;
    height: 100%;
    .cancle {
      cursor: pointer;
      margin: 12px;
      width: 96px;
      height: 34px;
      background: #ffffff;
      border: 1px solid #cdd3dc;
      border-radius: 2px;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: #4b5970;
      line-height: 34px;
    }
    .save {
      cursor: pointer;
      margin: 12px 0;
      width: 96px;
      height: 34px;
      background: #4076f3;
      border-radius: 2px;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
      line-height: 34px;
    }
  }
}
.serve-detial {
  width: 100%;
  height: 180px;
  .serve-detial-left {
    img {
      width: 300px;
      height: 176px;
      border-radius: 4px;
      margin: 0 20px;
    }
  }
  .serve-detial-right {
    .right-title {
      width: 306px;
      height: 36px;
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #232a3a;
    }
  }
}
table {
  width: 100%;
  margin: 5px 0px;
  border-collapse: collapse;
  border: 1px solid #ccc;
  tr {
    .title {
      width: 10%;
      background: #fafafa;
      border: 1px solid #e6eaef;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: #7d8da1;
    }
    .content {
      width: 40%;
      background: #ffffff;
      border: 1px solid #e6eaef;
      .server-edit {
        display: flex;
        align-items: center;
        span {
          margin-left: 8px;
          font-size: 14px;
          font-family: Source Han Sans CN, Source Han Sans CN-Regular;
          font-weight: 400;
          color: #7d8da1;
        }
        .edit-icon {
          cursor: pointer;
          img {
            margin: 5px 0 0 8px;
          }
        }
      }
      .server-url {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
        .server-url-item {
          margin: 0 10px;
        }
      }
      span {
        margin-left: 8px;
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN-Regular;
        font-weight: 400;
        color: #7d8da1;
      }
    }
    td {
      height: 36px;
    }
  }
}
.divider-header {
  font-size: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  color: #232a3a;
  margin: 10px 0 10px 20px;
}
.my-server {
  width: 97%;
  height: 56px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  .my-server-item {
    margin: 0 10px;
  }
}
.content-container {
  width: 100%;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin: 0 20px;
  padding: 10px;
  .my-server-item {
    margin: 0 10px;
  }
}
.el-switch {
  margin: 6px 5px;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
.defaultImg {
  width: 300px;
  height: 176px;
  border-radius: 4px;
  margin: 0 20px;
}
.network-analysis {
  padding: 15px 30px 15px 20px;
}
.server-edit {
  display: flex;
  align-items: center;
  span {
    margin-left: 8px;
    font-size: 14px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    color: #7d8da1;
    cursor: pointer;
  }
}
.attribute-box {
  margin: 10px 30px 10px 20px;
  border: 1px solid #e6eaef;
  padding: 20px 40px 10px 40px;
}
</style>
